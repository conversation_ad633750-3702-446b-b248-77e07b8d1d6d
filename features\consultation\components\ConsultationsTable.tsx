'use client';

import Image from 'next/image';
import Avatar from '@/features/patients/assets/AvarDefault.png';
import { ConsultationActions } from './ConsultationActions';
import { ConsultationTableProps } from '../types/consultationTypes';
import { useRouter } from 'next/navigation';

export function ConsultationsTable({
  consultations,
  onViewConsultation,
  onViewPatientProfile,
  onEditConsultation,
  onDeleteConsultation
}: ConsultationTableProps) {
  const router = useRouter();

  const handlePatientClick = (patientId: string) => {
    router.push(`/dashboard/pacientes/${patientId}/perfil`);
  };

  const handleConsultationClick = (consultationId: string) => {
    router.push(`/dashboard/consultas/${consultationId}/consulta`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getTypeLabel = (type: string) => {
    return type === 'presencial' ? 'Presencial' : 'Virtual';
  };

  return (
    <div className="w-full rounded-md border border-gray-200 bg-white h-full flex flex-col">
      <div className="flex-1 overflow-auto">
        <table className="w-full border-collapse">
          <thead className="sticky top-0 bg-white">
            <tr className="border-b border-gray-200">
              <th className="py-4 px-6 text-left font-medium text-gray-500">
                Nombre
              </th>
              <th className="py-4 px-6 text-left font-medium text-gray-500">
                Fecha de consulta
              </th>
              <th className="py-4 px-6 text-left font-medium text-gray-500">
                Motivo
              </th>
              <th className="py-4 px-6 text-left font-medium text-gray-500">
                Tipo
              </th>
              <th className="py-4 px-6 text-left font-medium text-gray-500">
                Cargo
              </th>
              <th className="py-4 px-6 text-center font-medium text-gray-500"></th>
            </tr>
          </thead>
          <tbody className="[&_tr:last-child]:border-0">
            {consultations.map((consultation) => (
              <tr
                key={consultation.id}
                className="border-b border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer"
                onClick={() => handleConsultationClick(consultation.id)}
              >
                <td 
                  className="py-4 px-6 p-2 align-middle"
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePatientClick(consultation.patientId);
                  }}
                >
                  <div className="flex items-center space-x-3">
                    <Image
                      src={Avatar}
                      alt={`Foto de ${consultation.patientName}`}
                      className="h-10 w-10 rounded-lg object-cover"
                    />
                    <span className="font-medium text-gray-900 hover:text-blue-600 transition-colors">
                      {consultation.patientName} {consultation.patientLastName}
                    </span>
                  </div>
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {formatDate(consultation.consultationDate)}
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {consultation.reason}
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {getTypeLabel(consultation.type)}
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {consultation.amount}
                </td>
                <td
                  className="py-4 px-6 p-2 align-middle text-center"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="flex justify-center items-center">
                    <ConsultationActions
                      consultation={consultation}
                      onViewConsultation={onViewConsultation}
                      onViewPatientProfile={onViewPatientProfile}
                      onEditConsultation={onEditConsultation}
                      onDeleteConsultation={onDeleteConsultation}
                    />
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
