'use client';
import React from 'react';
import BodyView from './BodyHighlighter';
import { Trash } from 'lucide-react';
import useConsultationStore from '../context/consultationStore';
import dynamic from 'next/dynamic';

const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });
import 'react-quill/dist/quill.snow.css';

const bodyPartsMap: { [key: string]: string } = {
  '1': 'Cabeza Zona Frontal',
  '2': 'Cabeza Zona Trasera',
  '3': 'Cuello Zona Frontal',
  '4': 'Cuello Zona Trasera',
  '5': 'Brazo Derecho Frontal',
  '6': 'Brazo Izquierdo Trasero',
  '7': 'Brazo Izquierdo Frontal',
  '8': 'Brazo Derecho Trasero',
  '9': 'Tórax',
  '10': 'Espalda',
  '11': '<PERSON><PERSON> Derecha Frontal',
  '12': '<PERSON><PERSON> Trasera',
  '13': '<PERSON><PERSON>a Frontal',
  '14': '<PERSON><PERSON> Trase<PERSON>',
  '15': '<PERSON><PERSON>vis <PERSON>',
  '16': 'Pelvis <PERSON> Trase<PERSON>'
};

const PhysicalExamination = () => {
  const {
    physicalExams,
    addPhysicalExam,
    updatePhysicalExamNote,
    removePhysicalExam
  } = useConsultationStore();

  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = event.target.value;
    if (selectedValue && !physicalExams[selectedValue]) {
      addPhysicalExam(selectedValue);
    }
  };

  const handleSVGClick = (part: string) => {
    if (!physicalExams[part]) {
      addPhysicalExam(part);
    }
  };

  const handleNoteChange = (part: string, value: string) => {
    updatePhysicalExamNote(part, value);
  };

  return (
    <div className="m-2 mx-auto max-w-4xl rounded-lg border border-gray-300 bg-white p-4 shadow-md md:flex md:gap-6 min-h-[600px]">
      <div className="w-full md:w-1/3">
        <h1 className="mb-2 text-sm font-semibold text-gray-800">
          Exploración Física
        </h1>
        <select
          onChange={handleSelectChange}
          className="w-full rounded border border-gray-300 bg-white p-2"
        >
          <option value="">Selecciona una zona</option>
          {Object.entries(bodyPartsMap).map(([key, name]) => (
            <option key={key} value={key}>
              {name}
            </option>
          ))}
        </select>

        {Object.entries(physicalExams).map(([key, note]) => (
          <div
            key={key}
            className="mt-3 w-full rounded border border-gray-300 p-3"
          >
            <div className="mb-2 flex items-center justify-between">
              <h2 className="text-md font-semibold">{bodyPartsMap[key]}</h2>
              <button
                onClick={() => removePhysicalExam(key)}
                className="text-red-500 hover:text-red-700"
              >
                <Trash size={18} />
              </button>
              {/* Editor de texto enriquecido */}
            </div>
            <ReactQuill
              theme="snow"
              value={note}
              onChange={(value) => handleNoteChange(key, value)}
              className="h-[150px] overflow-y-auto rounded bg-white"
            />
          </div>
        ))}
      </div>

      <div className="mt-4 flex w-full justify-center md:mt-0 md:w-2/3">
        <BodyView
          selectedParts={Object.keys(physicalExams)}
          onPartClick={handleSVGClick}
        />
      </div>
    </div>
  );
};

export default PhysicalExamination;
