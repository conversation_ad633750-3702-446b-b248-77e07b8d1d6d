'use server';

import { get } from '../../../lib/api';
import { getAuthToken } from '../../auth/actions/auth';
import { cookies } from 'next/headers';

/**
 * Función para verificar el estado de autenticación
 */
export async function checkAuthStatus() {
  try {
    const token = await getAuthToken();
    const tenantId = cookies().get('tenant_id')?.value;
    const authTokenCookie = cookies().get('auth_token')?.value;

    console.log('🔐 Verificando estado de autenticación...');
    console.log('Token desde getAuthToken():', token ? 'Presente' : 'Ausente');
    console.log('Tenant ID desde cookies:', tenantId ? 'Presente' : 'Ausente');
    console.log('Auth token cookie:', authTokenCookie ? 'Presente' : 'Ausente');

    return {
      hasToken: !!token,
      hasTenantId: !!tenantId,
      hasAuthCookie: !!authTokenCookie,
      tokenLength: token ? token.length : 0,
      tenantId: tenantId || 'No encontrado',
      // No mostramos el token completo por seguridad, solo los primeros y últimos caracteres
      tokenPreview: token ? `${token.substring(0, 10)}...${token.substring(token.length - 10)}` : 'No token'
    };
  } catch (error) {
    console.error('Error verificando autenticación:', error);
    return {
      error: error instanceof Error ? error.message : 'Error desconocido',
      hasToken: false,
      hasTenantId: false,
      hasAuthCookie: false
    };
  }
}

/**
 * Función de diagnóstico para probar diferentes endpoints y parámetros
 * del backend de pacientes
 */
export async function debugPatientsAPI() {
  // Basándome en el código del backend, el método findAll espera PaginationParams
  // que se procesan con parsePaginationAndSorting
  const testCases = [
    // Caso 1: Endpoint original con parámetros actuales
    { endpoint: '/patient?page=1&limit=10', description: 'Endpoint original (page/limit)' },

    // Caso 2: Sin parámetros de paginación
    { endpoint: '/patient', description: 'Sin parámetros' },

    // Caso 3: Parámetros diferentes (algunos backends usan pageSize en lugar de limit)
    { endpoint: '/patient?page=1&pageSize=10', description: 'Con pageSize' },

    // Caso 4: Parámetros con offset/limit
    { endpoint: '/patient?offset=0&limit=10', description: 'Con offset/limit' },

    // Caso 5: Endpoint plural
    { endpoint: '/patients?page=1&limit=10', description: 'Endpoint plural' },

    // Caso 6: Parámetros con take/skip (común en Prisma)
    { endpoint: '/patient?take=10&skip=0', description: 'Con take/skip (Prisma)' },

    // Caso 7: Parámetros estándar de paginación
    { endpoint: '/patient?page=1&size=10', description: 'Con page/size' },

    // Caso 8: Con orderBy (el backend lo soporta)
    { endpoint: '/patient?page=1&limit=10&orderBy=name&orderDirection=asc', description: 'Con ordenamiento' },
  ];

  const results = [];

  for (const testCase of testCases) {
    try {
      console.log(`\n🧪 Probando: ${testCase.description}`);
      console.log(`📡 Endpoint: ${testCase.endpoint}`);
      
      const response = await get<any>(testCase.endpoint);
      
      results.push({
        ...testCase,
        success: true,
        response: response,
        responseType: Array.isArray(response) ? 'array' : typeof response,
        hasItems: response?.items ? response.items.length : 'N/A',
        hasMeta: !!response?.meta,
        hasPatients: !!response?.patients,
      });
      
      console.log(`✅ Éxito: ${testCase.description}`);
      console.log(`📊 Tipo de respuesta: ${Array.isArray(response) ? 'array' : typeof response}`);
      
    } catch (error) {
      results.push({
        ...testCase,
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
      });
      
      console.log(`❌ Error: ${testCase.description}`);
      console.log(`🚨 Mensaje: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Resumen de resultados
  console.log('\n📋 RESUMEN DE PRUEBAS:');
  console.log('='.repeat(50));
  
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.description}: ${result.success ? '✅ ÉXITO' : '❌ ERROR'}`);
    if (result.success) {
      console.log(`   - Tipo: ${result.responseType}`);
      console.log(`   - Items: ${result.hasItems}`);
      console.log(`   - Meta: ${result.hasMeta ? 'Sí' : 'No'}`);
      console.log(`   - Patients: ${result.hasPatients ? 'Sí' : 'No'}`);
    } else {
      console.log(`   - Error: ${result.error}`);
    }
  });

  return results;
}

/**
 * Función para probar la conectividad básica con el backend
 */
export async function testBackendConnectivity() {
  try {
    console.log('🔍 Probando conectividad básica con el backend...');
    
    // Intentar un endpoint que debería existir (como health check o auth)
    const healthEndpoints = [
      '/health',
      '/api/health', 
      '/status',
      '/auth/profile', // Este sabemos que existe por el código
    ];

    for (const endpoint of healthEndpoints) {
      try {
        console.log(`📡 Probando: ${endpoint}`);
        const response = await get<any>(endpoint);
        console.log(`✅ ${endpoint} respondió correctamente`);
        return { success: true, workingEndpoint: endpoint, response };
      } catch (error) {
        console.log(`❌ ${endpoint} falló: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      }
    }

    return { success: false, message: 'Ningún endpoint de salud respondió' };
    
  } catch (error) {
    console.error('Error en test de conectividad:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Error desconocido' 
    };
  }
}
