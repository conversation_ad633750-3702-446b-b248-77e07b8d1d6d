'use server';

import { get } from '../../../lib/api';

/**
 * Función de diagnóstico para probar diferentes endpoints y parámetros
 * del backend de pacientes
 */
export async function debugPatientsAPI() {
  const testCases = [
    // Caso 1: Endpoint original con parámetros actuales
    { endpoint: '/patient?page=1&limit=10', description: 'Endpoint original' },
    
    // Caso 2: Sin parámetros de paginación
    { endpoint: '/patient', description: 'Sin parámetros' },
    
    // Caso 3: Parámetros diferentes (algunos backends usan pageSize en lugar de limit)
    { endpoint: '/patient?page=1&pageSize=10', description: 'Con pageSize' },
    
    // Caso 4: Parámetros con offset/limit
    { endpoint: '/patient?offset=0&limit=10', description: 'Con offset/limit' },
    
    // Caso 5: Endpoint plural
    { endpoint: '/patients?page=1&limit=10', description: 'Endpoint plural' },
    
    // Caso 6: Parámetros con take/skip (común en algunos ORMs)
    { endpoint: '/patient?take=10&skip=0', description: 'Con take/skip' },
  ];

  const results = [];

  for (const testCase of testCases) {
    try {
      console.log(`\n🧪 Probando: ${testCase.description}`);
      console.log(`📡 Endpoint: ${testCase.endpoint}`);
      
      const response = await get<any>(testCase.endpoint);
      
      results.push({
        ...testCase,
        success: true,
        response: response,
        responseType: Array.isArray(response) ? 'array' : typeof response,
        hasItems: response?.items ? response.items.length : 'N/A',
        hasMeta: !!response?.meta,
        hasPatients: !!response?.patients,
      });
      
      console.log(`✅ Éxito: ${testCase.description}`);
      console.log(`📊 Tipo de respuesta: ${Array.isArray(response) ? 'array' : typeof response}`);
      
    } catch (error) {
      results.push({
        ...testCase,
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
      });
      
      console.log(`❌ Error: ${testCase.description}`);
      console.log(`🚨 Mensaje: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Resumen de resultados
  console.log('\n📋 RESUMEN DE PRUEBAS:');
  console.log('='.repeat(50));
  
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.description}: ${result.success ? '✅ ÉXITO' : '❌ ERROR'}`);
    if (result.success) {
      console.log(`   - Tipo: ${result.responseType}`);
      console.log(`   - Items: ${result.hasItems}`);
      console.log(`   - Meta: ${result.hasMeta ? 'Sí' : 'No'}`);
      console.log(`   - Patients: ${result.hasPatients ? 'Sí' : 'No'}`);
    } else {
      console.log(`   - Error: ${result.error}`);
    }
  });

  return results;
}

/**
 * Función para probar la conectividad básica con el backend
 */
export async function testBackendConnectivity() {
  try {
    console.log('🔍 Probando conectividad básica con el backend...');
    
    // Intentar un endpoint que debería existir (como health check o auth)
    const healthEndpoints = [
      '/health',
      '/api/health', 
      '/status',
      '/auth/profile', // Este sabemos que existe por el código
    ];

    for (const endpoint of healthEndpoints) {
      try {
        console.log(`📡 Probando: ${endpoint}`);
        const response = await get<any>(endpoint);
        console.log(`✅ ${endpoint} respondió correctamente`);
        return { success: true, workingEndpoint: endpoint, response };
      } catch (error) {
        console.log(`❌ ${endpoint} falló: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      }
    }

    return { success: false, message: 'Ningún endpoint de salud respondió' };
    
  } catch (error) {
    console.error('Error en test de conectividad:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Error desconocido' 
    };
  }
}
