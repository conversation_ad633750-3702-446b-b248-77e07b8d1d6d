'use client';
import React, { useEffect, useState } from 'react';
import { getConsultations } from '@/features/consultation/actions/fetchConsultation';
import { Mail, ArrowLeft, Calendar } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import Avatar from '@/features/patients/assets/AvarDefault.png';
import { Button } from '@/components/ui/button';
import NavConsultation from '@/features/consultation/components/NavConsultation';
import PhysicalExamination from '@/features/consultation/components/PhysicalExamination';
import ConditionNotes from '@/features/consultation/components/ConditionNotes';
import ExamPhysical from '@/features/consultation/components/ExamPhysical';
import DiagnosisForm from '@/features/consultation/components/DiagnosisForm';
import { DashboardConsultation } from '@/features/consultation/types/consultationTypes';

const ConsultationPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const [consultation, setConsultation] = useState<DashboardConsultation | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchConsultation = async () => {
      try {
        // TODO: Replace with specific consultation endpoint when available
        const result = await getConsultations(1, 100); // Get all consultations
        const foundConsultation = result.consultations.find((c) => c.id === id);
        setConsultation(foundConsultation || null);
      } catch (error) {
        console.error('Error al cargar la consulta:', error);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchConsultation();
    }
  }, [id]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Cargando consulta...</div>
      </div>
    );
  }

  if (!consultation) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg text-red-600">Consulta no encontrada</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex-shrink-0 bg-white border-b">
        <div className="p-6">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4 text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Regresar
          </Button>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-4">
                <Image
                  src={Avatar}
                  alt={`Foto de ${consultation.patientName}`}
                  className="h-20 w-20 rounded-lg object-cover"
                />
                <div>
                  <h1 className="text-2xl font-bold text-blue-600 mb-1">
                    {consultation.patientName} {consultation.patientLastName}
                  </h1>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span className="flex items-center">
                      <Calendar className="mr-1 h-4 w-4" />
                      Consulta: {formatDate(consultation.consultationDate)}
                    </span>
                    <span className="flex items-center">
                      <Mail className="mr-1 h-4 w-4" />
                      {consultation.type === 'presencial' ? 'Presencial' : 'Virtual'}
                    </span>
                  </div>
                  <div className="mt-2">
                    <span className="text-sm text-gray-600">
                      <strong>Motivo:</strong> {consultation.reason}
                    </span>
                  </div>
                  <div className="mt-1">
                    <span className="text-sm text-gray-600">
                      <strong>Doctor:</strong> {consultation.doctorName}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  className="border-blue-500 text-blue-600 hover:bg-blue-50"
                >
                  ✓ Finalizar consulta
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="px-6 pb-4">
          <NavConsultation />
        </div>
      </div>

      {/* Main Content - Scrollable */}
      <div className="flex-1 overflow-auto bg-gray-50">
        <div className="p-6">
          <div className="flex flex-col gap-6 w-full">
            <section id="condition-notes">
              <ConditionNotes />
            </section>
            <section id="physical-examination">
              <PhysicalExamination />
            </section>
            <section id="exam-physical">
              <ExamPhysical />
            </section>
            <section id="diagnosis-form">
              <DiagnosisForm />
            </section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsultationPage;
