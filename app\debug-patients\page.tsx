import { debugPatientsAPI, testBackendConnectivity, checkAuthStatus } from '@/features/patients/actions/debugPatients';

export default async function DebugPatientsPage() {
  // Ejecutar las pruebas de diagnóstico
  const authStatus = await checkAuthStatus();
  const connectivityTest = await testBackendConnectivity();
  const apiTests = await debugPatientsAPI();

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">🔧 Diagnóstico de API de Pacientes</h1>
      
      <div className="space-y-6">
        {/* Estado de Autenticación */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">🔐 Estado de Autenticación</h2>
          <div className={`p-4 rounded ${authStatus.hasToken && authStatus.hasTenantId ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {authStatus.error ? (
              <div>
                <p>❌ Error verificando autenticación</p>
                <p>🚨 {authStatus.error}</p>
              </div>
            ) : (
              <div className="space-y-2">
                <p>🎫 Token: {authStatus.hasToken ? '✅ Presente' : '❌ Ausente'}</p>
                <p>🏢 Tenant ID: {authStatus.hasTenantId ? '✅ Presente' : '❌ Ausente'}</p>
                <p>🍪 Cookie de auth: {authStatus.hasAuthCookie ? '✅ Presente' : '❌ Ausente'}</p>
                {authStatus.hasToken && (
                  <div className="text-sm">
                    <p>📏 Longitud del token: {authStatus.tokenLength}</p>
                    <p>👀 Preview: <code>{authStatus.tokenPreview}</code></p>
                  </div>
                )}
                {authStatus.hasTenantId && (
                  <p className="text-sm">🏢 Tenant: <code>{authStatus.tenantId}</code></p>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Test de Conectividad */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">🔍 Test de Conectividad</h2>
          <div className={`p-4 rounded ${connectivityTest.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {connectivityTest.success ? (
              <div>
                <p>✅ Backend conectado correctamente</p>
                <p>📡 Endpoint funcional: {connectivityTest.workingEndpoint}</p>
              </div>
            ) : (
              <div>
                <p>❌ Problema de conectividad</p>
                <p>🚨 {connectivityTest.message}</p>
              </div>
            )}
          </div>
        </div>

        {/* Tests de API */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">🧪 Tests de Endpoints</h2>
          <div className="space-y-4">
            {apiTests.map((test, index) => (
              <div key={index} className={`p-4 rounded border-l-4 ${
                test.success 
                  ? 'border-green-500 bg-green-50' 
                  : 'border-red-500 bg-red-50'
              }`}>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">{test.description}</h3>
                  <span className={`px-2 py-1 rounded text-sm ${
                    test.success 
                      ? 'bg-green-200 text-green-800' 
                      : 'bg-red-200 text-red-800'
                  }`}>
                    {test.success ? 'ÉXITO' : 'ERROR'}
                  </span>
                </div>
                
                <p className="text-sm text-gray-600 mb-2">
                  📡 <code>{test.endpoint}</code>
                </p>
                
                {test.success ? (
                  <div className="text-sm space-y-1">
                    <p>📊 Tipo de respuesta: <code>{test.responseType}</code></p>
                    {test.hasItems !== 'N/A' && (
                      <p>📋 Items encontrados: <code>{test.hasItems}</code></p>
                    )}
                    <p>🏷️ Tiene meta: <code>{test.hasMeta ? 'Sí' : 'No'}</code></p>
                    <p>👥 Tiene patients: <code>{test.hasPatients ? 'Sí' : 'No'}</code></p>
                  </div>
                ) : (
                  <p className="text-sm text-red-600">
                    🚨 Error: {test.error}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Instrucciones */}
        <div className="bg-blue-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4 text-blue-800">📋 Instrucciones</h2>
          <div className="text-blue-700 space-y-2">
            <p>1. Revisa los resultados arriba para identificar qué endpoint funciona</p>
            <p>2. Si algún test es exitoso, usa ese formato en el código principal</p>
            <p>3. Si todos fallan, el problema está en el backend o la configuración</p>
            <p>4. Verifica la consola del navegador para logs adicionales</p>
            <p>5. Una vez identificado el problema, elimina esta página de debug</p>
          </div>
        </div>

        {/* Variables de entorno */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">⚙️ Configuración</h2>
          <div className="space-y-2 text-sm">
            <p>🌐 API URL: <code>{process.env.NEXT_PUBLIC_API_URL || 'No configurada'}</code></p>
            <p>🔧 Modo: Desarrollo</p>
          </div>
        </div>
      </div>
    </div>
  );
}
