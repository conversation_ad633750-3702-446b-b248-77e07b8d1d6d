'use client';
import React from 'react';
import VitalSings from './VitalSings';
import Archive<PERSON>onteiner from './ArchiveConteiner';
import ConsultNotes from './ConsultNotes';
import ClinicalSummary from './ClinicalSummary';

const ConditionNotes = () => {
  return (
    <div className="flex flex-col p-4 min-h-[600px]">
      <h1 className="mb-4 text-lg font-semibold text-gray-800">
        Notas de padecimiento
      </h1>
      <div className="grid h-full grid-cols-1 gap-4 md:grid-cols-[1fr_2fr_1fr]">
        <div className="flex h-full flex-col items-start justify-start gap-4">
          <VitalSings />
        </div>

        <div className="flex flex-col items-start justify-start gap-4">
          <ArchiveConteiner />
          <ConsultNotes />
        </div>

        <div className="h-full min-h-[400px]">
          <ClinicalSummary />
        </div>
      </div>
      {/* Temporary content to test scrolling */}
      <div className="mt-8 p-4 bg-gray-100 rounded">
        <p className="text-gray-600">Contenido adicional para probar el scroll...</p>
        {Array.from({ length: 10 }, (_, i) => (
          <p key={i} className="mt-2 text-sm text-gray-500">
            Línea de contenido {i + 1} para hacer que la sección sea más larga y poder probar el scroll.
          </p>
        ))}
      </div>
    </div>
  );
};

export default ConditionNotes;
