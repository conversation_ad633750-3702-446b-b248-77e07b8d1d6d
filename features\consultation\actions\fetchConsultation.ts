import consultations from '@/features/consultation/helpers/consultationList.json';
import { DashboardConsultation } from '../types/consultationTypes';
import { get } from '../../../lib/api';

// Define the expected response structure from the API
export interface GetConsultationsResponse {
  consultations: DashboardConsultation[];
  total: number;
}

/**
 * Fetches a list of consultations with pagination.
 * @param page The page number to fetch (default: 1).
 * @param pageSize The number of consultations per page (default: 10 for pagination).
 * @returns A promise resolving to the list of consultations and total count.
 */
// Set to true to use mock data, false to use real API
const USE_MOCK_DATA = false;

export async function getConsultations(
  page: number = 1,
  pageSize: number = 10
): Promise<GetConsultationsResponse> {
  try {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Calculate pagination
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedConsultations = consultations.slice(startIndex, endIndex);

      return {
        consultations: paginatedConsultations as DashboardConsultation[],
        total: consultations.length
      };
    } else {
      // Real API call - trying different possible endpoints
      let response: any;
      const possibleEndpoints = [
        `/consultation?page=${page}&limit=${pageSize}`,
        `/consultations?page=${page}&limit=${pageSize}`,
        `/appointment?page=${page}&limit=${pageSize}`,
        `/appointments?page=${page}&limit=${pageSize}`
      ];

      let lastError: any;

      for (const endpoint of possibleEndpoints) {
        try {
          console.log(`🔍 Trying consultation endpoint: ${endpoint}`);
          response = await get<any>(endpoint);
          console.log(`✅ Success with endpoint: ${endpoint}`);
          break;
        } catch (error) {
          console.log(`❌ Failed endpoint: ${endpoint}`, error instanceof Error ? error.message : error);
          lastError = error;
          continue;
        }
      }

      if (!response) {
        console.error('All consultation endpoints failed, falling back to mock data');
        // Fallback to mock data if all endpoints fail
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedConsultations = consultations.slice(startIndex, endIndex);

        return {
          consultations: paginatedConsultations as DashboardConsultation[],
          total: consultations.length
        };
      }

      console.log('Raw consultation API response:', response);
      console.log('Response type:', typeof response);
      console.log('Is array?', Array.isArray(response));
      console.log('Response keys:', response && typeof response === 'object' ? Object.keys(response) : 'N/A');

      // Handle different response formats
      let consultationsArray: any[] = [];
      let total = 0;

      if (Array.isArray(response)) {
        consultationsArray = response;
        total = response.length;
        console.log('⚠️  WARNING: Consultation response is a direct array with', consultationsArray.length, 'items');
      } else if (response && Array.isArray(response.consultations)) {
        consultationsArray = response.consultations;
        total = response.total || response.consultations.length;
        console.log('Response has consultations property with', consultationsArray.length, 'items');
      } else if (response && Array.isArray(response.items)) {
        consultationsArray = response.items;
        total = response.meta?.totalItems || response.total || response.items.length;
        console.log('Response has items property with', consultationsArray.length, 'items');
      } else if (response && Array.isArray(response.appointments)) {
        consultationsArray = response.appointments;
        total = response.total || response.appointments.length;
        console.log('Response has appointments property with', consultationsArray.length, 'items');
      } else {
        console.warn('Received unexpected format for consultations list:', response);
        // Fallback to mock data
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedConsultations = consultations.slice(startIndex, endIndex);

        return {
          consultations: paginatedConsultations as DashboardConsultation[],
          total: consultations.length
        };
      }

      // Transform the API response to match our DashboardConsultation type
      const transformedConsultations: DashboardConsultation[] = consultationsArray.map((c: any) => {
        return {
          id: String(c.id),
          patient_name: c.patient_name || c.patient?.name || c.user?.name || 'Nombre no disponible',
          patient_last_name: c.patient_last_name || c.patient?.last_name || c.user?.last_name || '',
          patient_image: c.patient_image || c.patient?.image || c.user?.image || '',
          date: c.date || c.appointment_date || c.created_at || new Date().toISOString(),
          time: c.time || c.appointment_time || '00:00',
          type: c.type || c.consultation_type || 'Consulta general',
          status: c.status || 'programada',
          doctor_name: c.doctor_name || c.doctor?.name || c.physician?.name || 'Doctor no asignado',
          notes: c.notes || c.description || ''
        };
      });

      console.log(`Successfully processed ${transformedConsultations.length} consultations.`);
      return {
        consultations: transformedConsultations,
        total
      };
    }
  } catch (error) {
    console.error('Error fetching consultations:', error);
    throw error;
  }
}

// Legacy function for backward compatibility
const FetchConsultations = () => {
  return consultations as DashboardConsultation[];
};

export default FetchConsultations;
