import consultations from '@/features/consultation/helpers/consultationList.json';
import { DashboardConsultation } from '../types/consultationTypes';

// Define the expected response structure from the API
export interface GetConsultationsResponse {
  consultations: DashboardConsultation[];
  total: number;
}

/**
 * Fetches a list of consultations with pagination.
 * @param page The page number to fetch (default: 1).
 * @param pageSize The number of consultations per page (default: 10 for pagination).
 * @returns A promise resolving to the list of consultations and total count.
 */
// Set to true to use mock data, false to use real API
const USE_MOCK_DATA = true;

export async function getConsultations(
  page: number = 1,
  pageSize: number = 10
): Promise<GetConsultationsResponse> {
  try {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Calculate pagination
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedConsultations = consultations.slice(startIndex, endIndex);

      return {
        consultations: paginatedConsultations as DashboardConsultation[],
        total: consultations.length
      };
    } else {
      // TODO: Replace with actual API call when backend is ready
      // const response = await get(`/consultation?page=${page}&limit=${pageSize}`);
      // return response.data;
      throw new Error('API integration not implemented yet');
    }
  } catch (error) {
    console.error('Error fetching consultations:', error);
    throw error;
  }
}

// Legacy function for backward compatibility
const FetchConsultations = () => {
  return consultations as DashboardConsultation[];
};

export default FetchConsultations;
