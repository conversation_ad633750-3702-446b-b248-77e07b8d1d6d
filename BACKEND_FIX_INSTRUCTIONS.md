# 🐛 Solución para el Error de calculateAge en el Backend

## Problema Identificado

El error ocurre en el archivo `back/src/utils/fuctions.ts` en la función `calculateAge`:

```
Error en findAll: TypeError: Cannot read properties of null (reading 'getFullYear')
at calculateAge (C:\Users\<USER>\OneDrive\Escritorio\Trabajo\back\src\utils\fuctions.ts:3:45)
```

## Causa

La función `calculateAge` está intentando llamar `getFullYear()` en un valor `null` cuando `user.birth_date` es `null` en la base de datos.

## Solución

### Opción 1: Modificar la función calculateAge (Recomendado)

En el archivo `back/src/utils/fuctions.ts`, cambiar la función `calculateAge`:

```typescript
// ANTES (código actual que falla)
export function calculateAge(birthDate: Date): number {
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
}

// DESPUÉS (código corregido)
export function calculateAge(birthDate: Date | null): number {
  // Manejar el caso cuando birthDate es null o undefined
  if (!birthDate) {
    return 0; // o return null, dependiendo de lo que prefieras
  }
  
  const today = new Date();
  const birth = new Date(birthDate);
  
  // Verificar que la fecha sea válida
  if (isNaN(birth.getTime())) {
    return 0;
  }
  
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
}
```

### Opción 2: Modificar el PatientService

En el archivo `back/src/management/patient/patient.service.ts`, en la línea 352 aproximadamente:

```typescript
// ANTES
return patients.map((patient) => {
  const user = patient.user;
  return {
    id: user.id,
    name: user.name,
    last_name: user.last_name || '',
    image: user.image,
    age: calculateAge(user.birth_date), // ← Esta línea falla
    // ... resto del código
  };
});

// DESPUÉS
return patients.map((patient) => {
  const user = patient.user;
  return {
    id: user.id,
    name: user.name,
    last_name: user.last_name || '',
    image: user.image,
    age: user.birth_date ? calculateAge(user.birth_date) : 0, // ← Verificar antes de llamar
    // ... resto del código
  };
});
```

## Recomendación

**Usar la Opción 1** porque:
1. Soluciona el problema de raíz
2. Previene errores similares en otras partes del código
3. Es más robusto y maneja casos edge

## Verificación

Después de aplicar la solución:

1. Reiniciar el servidor del backend
2. En el frontend, cambiar `USE_MOCK_DATA = false` en `features/patients/actions/getPatients.ts`
3. Probar la carga de pacientes en el dashboard

## Datos de Prueba

El paciente que está causando el problema tiene:
- Email: <EMAIL>
- birth_date: null (en la base de datos)

Esto sugiere que durante la creación del paciente, el campo `birth_date` no se está guardando correctamente o se está guardando como null.

## Prevención Futura

Considerar agregar validaciones en el DTO de creación de pacientes para asegurar que `birth_date` sea requerido y válido.
