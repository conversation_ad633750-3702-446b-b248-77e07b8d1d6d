# 🐛 Solución para el Error de calculateAge en el Backend

## Problema Identificado

El error ocurre en la función `calculateAge` cuando recibe un valor `null`:

```
Error en findAll: TypeError: Cannot read properties of null (reading 'getFullYear')
at calculateAge (C:\Users\<USER>\OneDrive\Escritorio\Trabajo\back\src\utils\fuctions.ts:3:45)
```

## Función Actual

```typescript
export function calculateAge(birthDate: Date): number {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }
  return age;
}
```

## Solución Recomendada: Modificar calculateAge

**Cambiar la función en `back/src/utils/fuctions.ts`:**

```typescript
export function calculateAge(birthDate: Date | null): number {
  // Manejar el caso cuando birthDate es null o undefined
  if (!birthDate) {
    return 0;
  }

  const today = new Date();
  const birth = new Date(birthDate);

  // Verificar que la fecha sea válida
  if (isNaN(birth.getTime())) {
    return 0;
  }

  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }

  return age;
}
```

## Por qué esta solución es mejor

1. **Robustez**: Maneja casos donde `birth_date` puede ser `null` o `undefined`
2. **Prevención**: Evita errores similares en otras partes del código que usen esta función
3. **Validación**: Verifica que la fecha sea válida antes de procesarla
4. **Mantenibilidad**: Centraliza el manejo de casos edge en una sola función

## Solución Temporal Aplicada

Actualmente se está usando una solución temporal en `PatientService`:

```typescript
age: user.birth_date ? calculateAge(user.birth_date) : 0,
```

Esta solución funciona pero requiere verificar `birth_date` en cada lugar donde se use `calculateAge`. La solución recomendada elimina esta necesidad.

## Investigación Adicional Requerida

Aunque la solución temporal funciona, es importante investigar por qué algunos registros tienen `birth_date` como `null`:

1. **Verificar la base de datos**: Revisar si hay registros con `birth_date` null
2. **Validar el proceso de creación**: Asegurar que el DTO de creación requiera `birth_date`
3. **Revisar migraciones**: Verificar si alguna migración pudo haber causado valores null

## Verificación

Después de aplicar la solución:
1. Reiniciar el servidor del backend
2. Probar la carga de pacientes en el dashboard
3. Verificar que no aparezcan errores en los logs
