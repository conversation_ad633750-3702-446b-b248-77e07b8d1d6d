'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { getPatients } from '@/features/patients/actions/getPatients';
import { DashboardPatient } from '@/features/patients/types/dashboardTypes';
import { createConsultation, CreateConsultationData } from '@/features/consultation/actions/createConsultation';



export default function NewConsultationPage() {
  const router = useRouter();
  const [patients, setPatients] = useState<DashboardPatient[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<CreateConsultationData>({
    consultation_reason: '',
    start: '',
    end: '',
    patient_id: '',
    physician_id: '',
    status: 'pendiente',
    comments: ''
  });

  // Fetch patients data
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        const result = await getPatients(1, 100); // Get first 100 patients
        setPatients(result.patients || []);
      } catch (error) {
        console.error('Error fetching patients:', error);
        toast.error('Error al cargar los pacientes');
      }
    };

    fetchPatients();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.consultation_reason || !formData.start || !formData.patient_id) {
      toast.error('Por favor complete todos los campos requeridos');
      return;
    }

    setIsSubmitting(true);
    try {
      // Add default physician_id if not provided
      const consultationData = {
        ...formData,
        physician_id: formData.physician_id || 'default-physician-id' // TODO: Get from user context
      };

      await createConsultation(consultationData);
      toast.success('Consulta creada exitosamente');
      router.push('/dashboard/consultas');
    } catch (error) {
      console.error('Error creating consultation:', error);
      toast.error('Error al crear la consulta');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof CreateConsultationData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateTimeChange = (date: string, time: string) => {
    const startDateTime = `${date}T${time}:00`;
    const endDateTime = new Date(startDateTime);
    endDateTime.setMinutes(endDateTime.getMinutes() + 30); // Default 30 minutes duration

    setFormData(prev => ({
      ...prev,
      start: startDateTime,
      end: endDateTime.toISOString()
    }));
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex-shrink-0 bg-white border-b">
        <div className="p-6">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4 text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Regresar
          </Button>

          <h1 className="text-2xl font-bold text-gray-900">Nueva Consulta</h1>
        </div>
      </div>

      {/* Form Content - Scrollable */}
      <div className="flex-1 overflow-auto bg-gray-50">
        <div className="p-6">
          <div className="max-w-2xl mx-auto">
            <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border p-6 space-y-6">
              {/* Patient Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Paciente *
                </label>
                <select
                  value={formData.patient_id}
                  onChange={(e) => handleInputChange('patient_id', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">Seleccione un paciente</option>
                  {patients.map(patient => (
                    <option key={patient.id} value={patient.id}>
                      {patient.name} {patient.last_name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Consultation Reason */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Motivo de la consulta *
                </label>
                <textarea
                  value={formData.consultation_reason}
                  onChange={(e) => handleInputChange('consultation_reason', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                  placeholder="Describa el motivo de la consulta"
                  required
                />
              </div>

              {/* Date and Time */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Fecha *
                  </label>
                  <input
                    type="date"
                    onChange={(e) => {
                      const time = formData.start.split('T')[1]?.split(':').slice(0, 2).join(':') || '09:00';
                      handleDateTimeChange(e.target.value, time);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hora *
                  </label>
                  <input
                    type="time"
                    onChange={(e) => {
                      const date = formData.start.split('T')[0] || new Date().toISOString().split('T')[0];
                      handleDateTimeChange(date, e.target.value);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
              </div>

              {/* Comments */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Comentarios adicionales
                </label>
                <textarea
                  value={formData.comments || ''}
                  onChange={(e) => handleInputChange('comments', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={2}
                  placeholder="Comentarios opcionales"
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-4 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isSubmitting}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isSubmitting ? 'Creando...' : 'Crear Consulta'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
