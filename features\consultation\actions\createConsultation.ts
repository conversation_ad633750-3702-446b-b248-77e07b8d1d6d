'use server';

import { post } from '@/lib/api';

export interface CreateConsultationData {
  consultation_reason: string;
  start: string; // ISO date string
  end: string; // ISO date string
  patient_id: string;
  physician_id: string;
  status?: 'atendida' | 'cancelada' | 'pendiente';
  comments?: string;
}

export interface ConsultationResponse {
  id: string;
  consultation_reason: string;
  start: string;
  end: string;
  patient_id: string;
  physician_id: string;
  status: string;
  comments?: string;
  tenant_id: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Creates a new consultation/appointment
 * @param data Consultation data
 * @returns Created consultation data
 */
export async function createConsultation(
  data: CreateConsultationData
): Promise<ConsultationResponse> {
  try {
    // Use the appointments endpoint to create a consultation
    return await post<ConsultationResponse>('/appointments', data);
  } catch (error) {
    console.error('Error creating consultation:', error);

    // Handle error
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(error.message as string);
    }

    throw new Error('Failed to create consultation');
  }
}
