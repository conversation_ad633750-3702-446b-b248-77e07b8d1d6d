'use client';

import {
  Search,
  Plus,
  ArrowUpDown,
  SortAsc,
  SortDesc,
  Calendar,
  Clock,
  X,
  Database
} from 'lucide-react';
import { TbSortAscendingLetters } from 'react-icons/tb';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { ConsultationSearchAndFilterProps, ConsultationSortCriteria } from '../types/consultationTypes';
import { Switch } from '@/components/ui/switch';

interface ExtendedConsultationSearchAndFilterProps extends ConsultationSearchAndFilterProps {
  useMockData?: boolean;
  onToggleMockData?: (useMock: boolean) => void;
}

export function SearchAndFilterBarConsultations({
  searchTerm,
  onSearchChange,
  sortBy,
  onSortChange,
  onAddConsultation,
  useMockData = true,
  onToggleMockData
}: ExtendedConsultationSearchAndFilterProps) {
  const handleSort = (criteria: ConsultationSortCriteria) => {
    onSortChange(criteria);
  };

  const getSortLabel = (criteria: ConsultationSortCriteria | null) => {
    switch (criteria) {
      case 'patient-name':
        return 'Por nombre de paciente';
      case 'date-asc':
        return 'Fecha más antigua';
      case 'date-desc':
        return 'Fecha más reciente';
      case 'recent':
        return 'Más recientes';
      default:
        return null;
    }
  };

  const getSortIcon = (criteria: ConsultationSortCriteria | null) => {
    switch (criteria) {
      case 'patient-name':
        return <TbSortAscendingLetters className="h-4 w-4" />;
      case 'date-asc':
        return <SortAsc className="h-4 w-4" />;
      case 'date-desc':
        return <SortDesc className="h-4 w-4" />;
      case 'recent':
        return <Clock className="h-4 w-4" />;
      default:
        return <ArrowUpDown className="h-4 w-4" />;
    }
  };

  const clearSort = () => {
    onSortChange(null);
  };

  return (
    <div className="flex items-center px-6 py-4 bg-gray-50 border-b border-gray-200">
      {/* Search Input */}
      <div className="relative w-80">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400 h-4 w-4" />
        <input
          type="text"
          placeholder="Buscar"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
          style={{ backgroundColor: '#FBFBFB' }}
        />
      </div>

      {/* Sort/Filter Area */}
      <div className="ml-4 flex items-center gap-2">
        {!sortBy ? (
          /* Sort Dropdown when no filter is active */
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="default"
                className="flex items-center gap-2 rounded-lg border-gray-300 hover:bg-gray-50"
                style={{ backgroundColor: '#FBFBFB' }}
              >
                <ArrowUpDown className="h-4 w-4" />
                Ordenar
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              side="bottom"
              align="center"
              className="w-56"
              style={{ backgroundColor: '#FBFBFB' }}
            >
              <DropdownMenuItem
                onSelect={() => handleSort('patient-name')}
                className="flex items-center gap-2 hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700"
              >
                <TbSortAscendingLetters className="h-4 w-4" />
                Por nombre de paciente
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => handleSort('date-desc')}
                className="flex items-center gap-2 hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700"
              >
                <SortDesc className="h-4 w-4" />
                Fecha más reciente
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => handleSort('date-asc')}
                className="flex items-center gap-2 hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700"
              >
                <SortAsc className="h-4 w-4" />
                Fecha más antigua
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          /* Active filter display with clear option */
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="default"
              className="flex items-center gap-2 rounded-lg border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100"
            >
              {getSortIcon(sortBy)}
              {getSortLabel(sortBy)}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearSort}
              className="text-red-500 border-red-200 hover:bg-red-50 hover:border-red-300"
            >
              <X className="h-4 w-4 mr-1" />
              Eliminar filtro
            </Button>
          </div>
        )}
      </div>

      {/* Data Source Toggle */}
      {onToggleMockData && (
        <div className="ml-4 flex items-center gap-2">
          <Database className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-600">
            {useMockData ? 'Mock' : 'Real'}
          </span>
          <Switch
            checked={useMockData}
            onCheckedChange={onToggleMockData}
            className="data-[state=checked]:bg-blue-500"
          />
        </div>
      )}

      {/* Add Consultation Button - pushed to the right */}
      <div className="ml-auto">
        <Button
          variant="outline"
          size="default"
          className="flex items-center gap-2 rounded-lg border-gray-300 hover:bg-gray-50"
          style={{ backgroundColor: '#FBFBFB' }}
          onClick={onAddConsultation}
        >
          <Plus className="h-4 w-4 text-gray-500" />
          Nueva consulta
        </Button>
      </div>
    </div>
  );
}
